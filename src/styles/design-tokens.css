/**
 * Design Tokens - Single Source of Truth
 * 
 * This file contains all design tokens for the application.
 * Modify these values to update the entire design system.
 * 
 * Color Format: OKLCH (Perceptually uniform color space)
 * - L: Lightness (0-1)
 * - C: Chroma (saturation, 0-0.4)
 * - H: <PERSON><PERSON> (0-360 degrees)
 * 
 * BRAND COLORS (from logo):
 * - Light green ring: #b4fd98
 * - Border green: #73ed47
 * - Dark green crescent: #0A4000
 * - White circle: #fff
 */

:root {
  /* === LIGHT THEME === */
  
  /* Base Colors */
  --background: oklch(1.0000 0 0);                    /* Pure white */
  --foreground: oklch(0.3211 0 0);                    /* Dark gray text */
  
  /* Surface Colors */
  --card: oklch(1.0000 0 0);                          /* White cards */
  --card-foreground: oklch(0.3211 0 0);               /* Dark text on cards */
  --popover: oklch(1.0000 0 0);                       /* White popovers */
  --popover-foreground: oklch(0.3211 0 0);            /* Dark text on popovers */
  
  /* Brand Colors - Based on Logo */
  --brand-light: oklch(0.9012 0.0891 142.3456);       /* #b4fd98 - Light green ring */
  --brand-medium: oklch(0.7234 0.1567 142.3456);      /* #73ed47 - Border green */
  --brand-dark: oklch(0.2345 0.0891 142.3456);        /* #0A4000 - Dark green crescent */
  --brand-white: oklch(1.0000 0 0);                   /* #fff - White circle */
  
  /* Primary Colors (using brand colors) */
  --primary: oklch(0.7234 0.1567 142.3456);           /* #73ed47 - Main brand green */
  --primary-foreground: oklch(1.0000 0 0);            /* White text on primary */
  --primary-light: oklch(0.9012 0.0891 142.3456);     /* #b4fd98 - Light brand green */
  --primary-dark: oklch(0.2345 0.0891 142.3456);      /* #0A4000 - Dark brand green */
  
  /* Secondary Colors */
  --secondary: oklch(0.4375 0.0929 159.3902);         /* Darker teal secondary */
  --secondary-foreground: oklch(1.0000 0 0);          /* White text on secondary */
  
  /* Neutral Colors */
  --muted: oklch(0.9166 0.0148 102.4717);             /* Light gray backgrounds */
  --muted-foreground: oklch(0.5382 0 0);              /* Medium gray text */
  --accent: oklch(0.7234 0.1567 142.3456);            /* Brand green accent */
  --accent-foreground: oklch(1.0000 0 0);             /* White text on accent */
  
  /* Semantic Colors */
  --destructive: oklch(0.6766 0.1260 25.1211);        /* Red for errors/danger */
  --destructive-foreground: oklch(1.0000 0 0);        /* White text on destructive */
  
  /* Border & Input Colors */
  --border: oklch(0.8699 0 0);                        /* Light gray borders */
  --input: oklch(0.8699 0 0);                         /* Input field borders */
  --ring: oklch(0.7234 0.1567 142.3456);              /* Brand green focus ring */
  
  /* Progress Bar Colors */
  --progress-bg: oklch(0.9012 0.0891 142.3456);       /* Light brand green background */
  --progress-fill: oklch(0.7234 0.1567 142.3456);     /* Brand green fill */
  --progress-border: oklch(0.7234 0.1567 142.3456);   /* Brand green border */
  --progress-text: oklch(0.2345 0.0891 142.3456);     /* Dark brand green text */
  
  /* Chart Colors */
  --chart-1: oklch(0.7234 0.1567 142.3456);           /* Brand green chart color */
  --chart-2: oklch(0.7196 0.0906 267.0774);           /* Purple chart color */
  --chart-3: oklch(0.8118 0.0701 218.3524);           /* Blue chart color */
  --chart-4: oklch(0.6019 0.0723 251.0410);           /* Indigo chart color */
  --chart-5: oklch(0.5737 0.1247 152.5238);           /* Green chart color */
  
  /* Sidebar Colors */
  --sidebar: oklch(1.0000 0 0);                       /* White sidebar */
  --sidebar-foreground: oklch(0.3211 0 0);            /* Dark sidebar text */
  --sidebar-primary: oklch(0.7234 0.1567 142.3456);   /* Brand green sidebar elements */
  --sidebar-primary-foreground: oklch(1.0000 0 0);    /* White text on sidebar primary */
  --sidebar-accent: oklch(1.0000 0 0);                /* Accent sidebar elements */
  --sidebar-accent-foreground: oklch(0.3211 0 0);     /* Dark text on sidebar accent */
  --sidebar-border: oklch(0.8699 0 0);                /* Sidebar borders */
  --sidebar-ring: oklch(0.7234 0.1567 142.3456);      /* Brand green sidebar focus rings */
  
  /* Typography */
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  
  /* Border Radius */
  --radius: 0.5rem;
  
  /* Shadows */
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  
  /* Comprehensive Spacing Scale */
  --spacing-0: 0rem;                    /* 0px */
  --spacing-1: 0.25rem;                 /* 4px */
  --spacing-2: 0.5rem;                  /* 8px */
  --spacing-3: 0.75rem;                 /* 12px */
  --spacing-4: 1rem;                    /* 16px */
  --spacing-5: 1.25rem;                 /* 20px */
  --spacing-6: 1.5rem;                  /* 24px */
  --spacing-8: 2rem;                    /* 32px */
  --spacing-10: 2.5rem;                 /* 40px */
  --spacing-12: 3rem;                   /* 48px */
  --spacing-16: 4rem;                   /* 64px */
  --spacing-20: 5rem;                   /* 80px */
  --spacing-24: 6rem;                   /* 96px */
  --spacing-32: 8rem;                   /* 128px */
  
  /* Typography Scale */
  --font-size-xs: 0.75rem;              /* 12px */
  --font-size-sm: 0.875rem;             /* 14px */
  --font-size-base: 1rem;               /* 16px */
  --font-size-lg: 1.125rem;             /* 18px */
  --font-size-xl: 1.25rem;              /* 20px */
  --font-size-2xl: 1.5rem;              /* 24px */
  --font-size-3xl: 1.875rem;            /* 30px */
  --font-size-4xl: 2.25rem;             /* 36px */
  --font-size-5xl: 3rem;                /* 48px */
  --font-size-6xl: 3.75rem;             /* 60px */
  
  /* Font Weights */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Letter Spacing */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
  
  /* Border Radius Scale */
  --radius-none: 0;
  --radius-sm: 0.125rem;                /* 2px */
  --radius-md: 0.375rem;                /* 6px */
  --radius-lg: 0.5rem;                  /* 8px */
  --radius-xl: 0.75rem;                 /* 12px */
  --radius-2xl: 1rem;                   /* 16px */
  --radius-3xl: 1.5rem;                 /* 24px */
  --radius-full: 9999px;
  
  /* Animation & Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --transition-bounce: 250ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-spring: 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Container Max Widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

.dark {
  /* === DARK THEME === */
  
  /* Base Colors */
  --background: oklch(0.2393 0.0100 268.2607);        /* Dark blue background */
  --foreground: oklch(0.9219 0 0);                    /* Light gray text */
  
  /* Surface Colors */
  --card: oklch(0.3337 0.0065 229.0172);              /* Dark blue cards */
  --card-foreground: oklch(0.9219 0 0);               /* Light text on cards */
  --popover: oklch(0.3337 0.0065 229.0172);           /* Dark blue popovers */
  --popover-foreground: oklch(0.9219 0 0);            /* Light text on popovers */
  
  /* Brand Colors - Same as light theme for consistency */
  --brand-light: oklch(0.9012 0.0891 142.3456);       /* #b4fd98 - Light green ring */
  --brand-medium: oklch(0.7234 0.1567 142.3456);      /* #73ed47 - Border green */
  --brand-dark: oklch(0.2345 0.0891 142.3456);        /* #0A4000 - Dark green crescent */
  --brand-white: oklch(1.0000 0 0);                   /* #fff - White circle */
  
  /* Primary Colors (using brand colors) */
  --primary: oklch(0.7234 0.1567 142.3456);           /* #73ed47 - Main brand green */
  --primary-foreground: oklch(1.0000 0 0);            /* White text on primary */
  --primary-light: oklch(0.9012 0.0891 142.3456);     /* #b4fd98 - Light brand green */
  --primary-dark: oklch(0.2345 0.0891 142.3456);      /* #0A4000 - Dark brand green */
  
  /* Secondary Colors */
  --secondary: oklch(0.6019 0.0723 251.0410);         /* Purple secondary */
  --secondary-foreground: oklch(0.9219 0 0);          /* Light text on secondary */
  
  /* Neutral Colors */
  --muted: oklch(0.3867 0 0);                         /* Dark gray backgrounds */
  --muted-foreground: oklch(0.7155 0 0);              /* Light gray text */
  --accent: oklch(0.7234 0.1567 142.3456);            /* Brand green accent */
  --accent-foreground: oklch(1.0000 0 0);             /* White text on accent */
  
  /* Semantic Colors */
  --destructive: oklch(0.6766 0.1260 25.1211);        /* Same red for errors */
  --destructive-foreground: oklch(1.0000 0 0);        /* White text on destructive */
  
  /* Border & Input Colors */
  --border: oklch(0.3867 0 0);                        /* Dark gray borders */
  --input: oklch(0.3867 0 0);                         /* Dark input fields */
  --ring: oklch(0.7234 0.1567 142.3456);              /* Brand green focus ring */
  
  /* Progress Bar Colors - Dark theme adjustments */
  --progress-bg: oklch(0.3867 0 0);                   /* Dark background for progress */
  --progress-fill: oklch(0.7234 0.1567 142.3456);     /* Brand green fill */
  --progress-border: oklch(0.7234 0.1567 142.3456);   /* Brand green border */
  --progress-text: oklch(0.9219 0 0);                 /* Light text for dark theme */
  
  /* Chart Colors */
  --chart-1: oklch(0.7234 0.1567 142.3456);           /* Brand green chart color */
  --chart-2: oklch(0.6019 0.0723 251.0410);           /* Purple chart color */
  --chart-3: oklch(0.7196 0.0906 267.0774);           /* Blue chart color */
  --chart-4: oklch(0.8118 0.0701 218.3524);           /* Light blue chart color */
  --chart-5: oklch(0.5737 0.1247 152.5238);           /* Green chart color */
  
  /* Sidebar Colors */
  --sidebar: oklch(0.2393 0.0100 268.2607);           /* Dark sidebar */
  --sidebar-foreground: oklch(0.9219 0 0);            /* Light sidebar text */
  --sidebar-primary: oklch(0.7234 0.1567 142.3456);   /* Brand green sidebar elements */
  --sidebar-primary-foreground: oklch(1.0000 0 0);    /* White text on sidebar primary */
  --sidebar-accent: oklch(0.7196 0.0906 267.0774);    /* Purple sidebar accent */
  --sidebar-accent-foreground: oklch(0.9219 0 0);     /* Light text on sidebar accent */
  --sidebar-border: oklch(0.3867 0 0);                /* Dark sidebar borders */
  --sidebar-ring: oklch(0.7234 0.1567 142.3456);      /* Brand green sidebar focus rings */
  
  /* Typography (same as light) */
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  
  /* Border Radius (same as light) */
  --radius: 0.5rem;
  
  /* Shadows (same as light) */
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  
  /* Comprehensive Spacing Scale (same as light) */
  --spacing-0: 0rem;                    /* 0px */
  --spacing-1: 0.25rem;                 /* 4px */
  --spacing-2: 0.5rem;                  /* 8px */
  --spacing-3: 0.75rem;                 /* 12px */
  --spacing-4: 1rem;                    /* 16px */
  --spacing-5: 1.25rem;                 /* 20px */
  --spacing-6: 1.5rem;                  /* 24px */
  --spacing-8: 2rem;                    /* 32px */
  --spacing-10: 2.5rem;                 /* 40px */
  --spacing-12: 3rem;                   /* 48px */
  --spacing-16: 4rem;                   /* 64px */
  --spacing-20: 5rem;                   /* 80px */
  --spacing-24: 6rem;                   /* 96px */
  --spacing-32: 8rem;                   /* 128px */
  
  /* Typography Scale (same as light) */
  --font-size-xs: 0.75rem;              /* 12px */
  --font-size-sm: 0.875rem;             /* 14px */
  --font-size-base: 1rem;               /* 16px */
  --font-size-lg: 1.125rem;             /* 18px */
  --font-size-xl: 1.25rem;              /* 20px */
  --font-size-2xl: 1.5rem;              /* 24px */
  --font-size-3xl: 1.875rem;            /* 30px */
  --font-size-4xl: 2.25rem;             /* 36px */
  --font-size-5xl: 3rem;                /* 48px */
  --font-size-6xl: 3.75rem;             /* 60px */
  
  /* Font Weights (same as light) */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* Line Heights (same as light) */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Letter Spacing (same as light) */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
  
  /* Border Radius Scale (same as light) */
  --radius-none: 0;
  --radius-sm: 0.125rem;                /* 2px */
  --radius-md: 0.375rem;                /* 6px */
  --radius-lg: 0.5rem;                  /* 8px */
  --radius-xl: 0.75rem;                 /* 12px */
  --radius-2xl: 1rem;                   /* 16px */
  --radius-3xl: 1.5rem;                 /* 24px */
  --radius-full: 9999px;
  
  /* Animation & Transitions (same as light) */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  --transition-bounce: 250ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-spring: 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* Z-Index Scale (same as light) */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Breakpoints (same as light) */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* Container Max Widths (same as light) */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/**
 * COMPREHENSIVE DESIGN SYSTEM
 * 
 * This design system provides a complete foundation for consistent UI development.
 * All values are centralized here for easy maintenance and updates.
 * 
 * 1. BRAND COLOR PALETTE (from logo):
 *    - Light Green: #b4fd98 (--brand-light) - Light green ring
 *    - Medium Green: #73ed47 (--brand-medium) - Border green, primary color
 *    - Dark Green: #0A4000 (--brand-dark) - Dark green crescent
 *    - White: #fff (--brand-white) - White circle
 * 
 * 2. COLOR USAGE GUIDELINES:
 *    - Primary: Use --primary (brand-medium) for main actions, buttons, links
 *    - Accent: Use --primary-light for highlights, hover states
 *    - Background: Use --primary-dark for emphasis, important sections
 *    - Progress: Use --progress-fill for progress bars, loading states
 *    - Semantic: Use --destructive for errors, --success for positive actions
 * 
 * 3. TYPOGRAPHY SYSTEM:
 *    - Font Families: Plus Jakarta Sans (sans), Source Serif 4 (serif), JetBrains Mono (mono)
 *    - Font Sizes: xs (12px) to 6xl (60px) with consistent scale
 *    - Font Weights: thin (100) to black (900) for hierarchy
 *    - Line Heights: none (1) to loose (2) for readability
 *    - Letter Spacing: tighter (-0.05em) to widest (0.1em) for emphasis
 * 
 * 4. SPACING SYSTEM:
 *    - Base unit: 0.25rem (4px)
 *    - Scale: 0 to 32 (0px to 128px)
 *    - Consistent 4px grid system throughout
 *    - Use --spacing-{number} for consistent spacing
 * 
 * 5. BORDER RADIUS SYSTEM:
 *    - Scale: none (0) to full (9999px)
 *    - Common: sm (2px), md (6px), lg (8px), xl (12px)
 *    - Use --radius-{size} for consistent rounded corners
 * 
 * 6. SHADOW SYSTEM:
 *    - Progressive depth: 2xs to 2xl
 *    - Consistent opacity and blur values
 *    - Use for elevation and depth perception
 * 
 * 7. ANIMATION & TRANSITIONS:
 *    - Fast: 150ms for micro-interactions
 *    - Normal: 250ms for standard transitions
 *    - Slow: 350ms for complex animations
 *    - Bounce: Custom easing for playful interactions
 *    - Spring: Natural motion for organic feel
 * 
 * 8. Z-INDEX SYSTEM:
 *    - Organized scale from 1000 to 1080
 *    - Dropdown (1000) to Toast (1080)
 *    - Prevents z-index conflicts
 * 
 * 9. RESPONSIVE BREAKPOINTS:
 *    - Mobile-first approach
 *    - sm (640px), md (768px), lg (1024px), xl (1280px), 2xl (1536px)
 *    - Container max-widths for content constraints
 * 
 * 10. PROGRESS BAR SYSTEM:
 *     - Background: --progress-bg (theme-aware)
 *     - Fill: --progress-fill (brand green)
 *     - Border: --progress-border (brand green)
 *     - Text: --progress-text (theme-aware)
 * 
 * 11. ACCESSIBILITY FEATURES:
 *     - OKLCH color space for perceptual uniformity
 *     - High contrast ratios maintained
 *     - Focus rings clearly visible
 *     - Semantic color usage
 * 
 * USAGE EXAMPLES:
 * 
 * Typography:
 *   font-size: var(--font-size-lg);
 *   font-weight: var(--font-weight-semibold);
 *   line-height: var(--line-height-tight);
 * 
 * Spacing:
 *   padding: var(--spacing-4);
 *   margin: var(--spacing-6);
 *   gap: var(--spacing-2);
 * 
 * Colors:
 *   background-color: var(--primary);
 *   color: var(--primary-foreground);
 *   border-color: var(--border);
 * 
 * Transitions:
 *   transition: var(--transition-normal);
 *   transition: var(--transition-bounce);
 * 
 * To customize your design:
 * 1. Modify the OKLCH values for colors
 * 2. Adjust spacing scale for different density
 * 3. Update typography scale for different hierarchy
 * 4. Test in both light and dark modes
 * 5. Ensure brand colors are consistent across all components
 * 6. Use the comprehensive token system for all styling
 */
