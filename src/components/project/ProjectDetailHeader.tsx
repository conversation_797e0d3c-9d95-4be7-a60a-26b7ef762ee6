"use client";

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarButton } from "@/components/ui/sidebar-button";
import { useAnalytics } from "@/hooks/useAnalytics";
import { ICON_SIZES } from "@/lib/constants";
import { ArrowLeft, HelpCircle } from "lucide-react";
import { useState } from "react";

interface ProjectDetailHeaderProps {
  selectedBusinessItem: any;
  onBackToItems: () => void;
}

export function ProjectDetailHeader({
  selectedBusinessItem,
  onBackToItems,
}: ProjectDetailHeaderProps) {
  const { trackClick, trackCustomEvent } = useAnalytics();
  const [isHelpOpen, setIsHelpOpen] = useState(false);
  const { state, isMobile } = useSidebar();

  return (
    <header className="flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border">
      <div className="flex items-center justify-between w-full h-full px-4">
        {/* Left side - Back button on mobile only, Item Title when sidebar is collapsed */}
        <div className="flex items-center gap-4 h-full">
          {isMobile && (
            <SidebarButton
              onClick={() => {
                trackClick("back-to-items", "project-detail-header");
                trackCustomEvent("navigation_clicked", {
                  destination: "items",
                  from_page: "item-detail",
                  location: "header",
                });
                onBackToItems();
              }}
              icon={ArrowLeft}
              variant="ghost"
              size="lg"
              layout="icon-only"
              showBorder={true}
              hoverColor="grey"
              hoverScale={true}
              iconClassName={ICON_SIZES.lg}
            />
          )}
          {state === "collapsed" && (
            <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {selectedBusinessItem?.title || "Untitled Item"}
            </h1>
          )}
        </div>

        {/* Right side - Help button with popover */}
        <div className="flex items-center gap-2 h-full">
          <Popover open={isHelpOpen} onOpenChange={setIsHelpOpen}>
            <PopoverTrigger>
              <SidebarButton
                icon={HelpCircle}
                variant="ghost"
                size="lg"
                layout="icon-only"
                showBorder={true}
                hoverColor="grey"
                hoverScale={true}
                onClick={() => {
                  trackClick("help-button", "project-header");
                  trackCustomEvent("help_clicked", {
                    from_item: selectedBusinessItem?.title,
                    location: "header",
                  });
                }}
                iconClassName={ICON_SIZES.lg}
              />
            </PopoverTrigger>
            <PopoverContent align="end" side="bottom" className="w-80">
              <div className="space-y-3">
                <h3 className="font-semibold text-sm">Table Guide</h3>
                <div className="space-y-2 text-xs">
                  <div className="flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>
                      <strong>Idea:</strong> What is the main idea of the item?
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span>
                      <strong>Action:</strong> What was done to achieve the idea?
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>
                      <strong>Result:</strong> What was the outcome of the action?
                    </span>
                  </div>
                  <div className="flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>
                      <strong>Status:</strong> Current state
                    </span>
                  </div>
                </div>
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Click cells to edit • Drag rows to reorder • Use + to add items
                  </p>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </header>
  );
}
