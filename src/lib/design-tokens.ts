/**
 * Design Token Constants
 *
 * This file provides TypeScript constants for design tokens,
 * making them easier to use in components and ensuring type safety.
 */

// Brand Colors (from logo)
export const BRAND_COLORS = {
  LIGHT: "#b4fd98", // Light green ring
  MEDIUM: "#73ed47", // Border green
  DARK: "#0A4000", // Dark green crescent
  WHITE: "#fff", // White circle
} as const;

// CSS Variable Names
export const CSS_VARS = {
  // Brand Colors
  BRAND_LIGHT: "var(--brand-light)",
  BRAND_MEDIUM: "var(--brand-medium)",
  BRAND_DARK: "var(--brand-dark)",
  BRAND_WHITE: "var(--brand-white)",

  // Primary Colors
  PRIMARY: "var(--primary)",
  PRIMARY_LIGHT: "var(--primary-light)",
  PRIMARY_DARK: "var(--primary-dark)",
  PRIMARY_FOREGROUND: "var(--primary-foreground)",

  // Progress Bar Colors
  PROGRESS_BG: "var(--progress-bg)",
  PROGRESS_FILL: "var(--progress-fill)",
  PROGRESS_BORDER: "var(--progress-border)",
  PROGRESS_TEXT: "var(--progress-text)",

  // Semantic Colors
  DESTRUCTIVE: "var(--destructive)",
  DESTRUCTIVE_FOREGROUND: "var(--destructive-foreground)",
  ACCENT: "var(--accent)",
  ACCENT_FOREGROUND: "var(--accent-foreground)",

  // Surface Colors
  BACKGROUND: "var(--background)",
  FOREGROUND: "var(--foreground)",
  CARD: "var(--card)",
  CARD_FOREGROUND: "var(--card-foreground)",
  POPOVER: "var(--popover)",
  POPOVER_FOREGROUND: "var(--popover-foreground)",

  // Border & Input Colors
  BORDER: "var(--border)",
  INPUT: "var(--input)",
  RING: "var(--ring)",

  // Neutral Colors
  MUTED: "var(--muted)",
  MUTED_FOREGROUND: "var(--muted-foreground)",

  // Chart Colors
  CHART_1: "var(--chart-1)",
  CHART_2: "var(--chart-2)",
  CHART_3: "var(--chart-3)",
  CHART_4: "var(--chart-4)",
  CHART_5: "var(--chart-5)",

  // Typography
  FONT_SIZE_XS: "var(--font-size-xs)",
  FONT_SIZE_SM: "var(--font-size-sm)",
  FONT_SIZE_BASE: "var(--font-size-base)",
  FONT_SIZE_LG: "var(--font-size-lg)",
  FONT_SIZE_XL: "var(--font-size-xl)",
  FONT_SIZE_2XL: "var(--font-size-2xl)",
  FONT_SIZE_3XL: "var(--font-size-3xl)",
  FONT_SIZE_4XL: "var(--font-size-4xl)",
  FONT_SIZE_5XL: "var(--font-size-5xl)",
  FONT_SIZE_6XL: "var(--font-size-6xl)",

  // Font Weights
  FONT_WEIGHT_THIN: "var(--font-weight-thin)",
  FONT_WEIGHT_LIGHT: "var(--font-weight-light)",
  FONT_WEIGHT_NORMAL: "var(--font-weight-normal)",
  FONT_WEIGHT_MEDIUM: "var(--font-weight-medium)",
  FONT_WEIGHT_SEMIBOLD: "var(--font-weight-semibold)",
  FONT_WEIGHT_BOLD: "var(--font-weight-bold)",
  FONT_WEIGHT_EXTRABOLD: "var(--font-weight-extrabold)",
  FONT_WEIGHT_BLACK: "var(--font-weight-black)",

  // Line Heights
  LINE_HEIGHT_NONE: "var(--line-height-none)",
  LINE_HEIGHT_TIGHT: "var(--line-height-tight)",
  LINE_HEIGHT_SNUG: "var(--line-height-snug)",
  LINE_HEIGHT_NORMAL: "var(--line-height-normal)",
  LINE_HEIGHT_RELAXED: "var(--line-height-relaxed)",
  LINE_HEIGHT_LOOSE: "var(--line-height-loose)",

  // Letter Spacing
  TRACKING_TIGHTER: "var(--tracking-tighter)",
  TRACKING_TIGHT: "var(--tracking-tight)",
  TRACKING_NORMAL: "var(--tracking-normal)",
  TRACKING_WIDE: "var(--tracking-wide)",
  TRACKING_WIDER: "var(--tracking-wider)",
  TRACKING_WIDEST: "var(--tracking-widest)",

  // Spacing
  SPACING_0: "var(--spacing-0)",
  SPACING_1: "var(--spacing-1)",
  SPACING_2: "var(--spacing-2)",
  SPACING_3: "var(--spacing-3)",
  SPACING_4: "var(--spacing-4)",
  SPACING_5: "var(--spacing-5)",
  SPACING_6: "var(--spacing-6)",
  SPACING_8: "var(--spacing-8)",
  SPACING_10: "var(--spacing-10)",
  SPACING_12: "var(--spacing-12)",
  SPACING_16: "var(--spacing-16)",
  SPACING_20: "var(--spacing-20)",
  SPACING_24: "var(--spacing-24)",
  SPACING_32: "var(--spacing-32)",

  // Border Radius
  RADIUS_NONE: "var(--radius-none)",
  RADIUS_SM: "var(--radius-sm)",
  RADIUS_MD: "var(--radius-md)",
  RADIUS_LG: "var(--radius-lg)",
  RADIUS_XL: "var(--radius-xl)",
  RADIUS_2XL: "var(--radius-2xl)",
  RADIUS_3XL: "var(--radius-3xl)",
  RADIUS_FULL: "var(--radius-full)",

  // Transitions
  TRANSITION_FAST: "var(--transition-fast)",
  TRANSITION_NORMAL: "var(--transition-normal)",
  TRANSITION_SLOW: "var(--transition-slow)",
  TRANSITION_BOUNCE: "var(--transition-bounce)",
  TRANSITION_SPRING: "var(--transition-spring)",

  // Z-Index
  Z_DROPDOWN: "var(--z-dropdown)",
  Z_STICKY: "var(--z-sticky)",
  Z_FIXED: "var(--z-fixed)",
  Z_MODAL_BACKDROP: "var(--z-modal-backdrop)",
  Z_MODAL: "var(--z-modal)",
  Z_POPOVER: "var(--z-popover)",
  Z_TOOLTIP: "var(--z-tooltip)",
  Z_TOAST: "var(--z-toast)",

  // Breakpoints
  BREAKPOINT_SM: "var(--breakpoint-sm)",
  BREAKPOINT_MD: "var(--breakpoint-md)",
  BREAKPOINT_LG: "var(--breakpoint-lg)",
  BREAKPOINT_XL: "var(--breakpoint-xl)",
  BREAKPOINT_2XL: "var(--breakpoint-2xl)",

  // Container Max Widths
  CONTAINER_SM: "var(--container-sm)",
  CONTAINER_MD: "var(--container-md)",
  CONTAINER_LG: "var(--container-lg)",
  CONTAINER_XL: "var(--container-xl)",
  CONTAINER_2XL: "var(--container-2xl)",
} as const;

// Tailwind CSS Classes for Common Patterns
export const TAILWIND_CLASSES = {
  // Progress Bar
  PROGRESS_BAR:
    "w-full bg-[var(--progress-bg)] border border-[var(--progress-border)] rounded-full",
  PROGRESS_FILL: "bg-[var(--progress-fill)] h-full rounded-full transition-all",
  PROGRESS_TEXT: "text-[var(--progress-text)]",

  // Buttons
  BUTTON_PRIMARY:
    "bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)]",
  BUTTON_SECONDARY:
    "bg-[var(--secondary)] hover:bg-[var(--secondary)]/90 text-[var(--secondary-foreground)]",
  BUTTON_DESTRUCTIVE:
    "bg-[var(--destructive)] hover:bg-[var(--destructive)]/90 text-[var(--destructive-foreground)]",

  // Cards
  CARD: "bg-[var(--card)] text-[var(--card-foreground)] border border-[var(--border)]",

  // Focus States
  FOCUS_RING: "focus:ring-2 focus:ring-[var(--ring)] focus:ring-offset-2",
} as const;

// Comprehensive Spacing Scale (in rem)
export const SPACING = {
  0: "0rem", // 0px
  1: "0.25rem", // 4px
  2: "0.5rem", // 8px
  3: "0.75rem", // 12px
  4: "1rem", // 16px
  5: "1.25rem", // 20px
  6: "1.5rem", // 24px
  8: "2rem", // 32px
  10: "2.5rem", // 40px
  12: "3rem", // 48px
  16: "4rem", // 64px
  20: "5rem", // 80px
  24: "6rem", // 96px
  32: "8rem", // 128px
} as const;

// Border Radius Scale
export const BORDER_RADIUS = {
  NONE: "0",
  SM: "0.125rem", // 2px
  MD: "0.375rem", // 6px
  LG: "0.5rem", // 8px
  XL: "0.75rem", // 12px
  "2XL": "1rem", // 16px
  "3XL": "1.5rem", // 24px
  FULL: "9999px",
} as const;

// Typography System
export const TYPOGRAPHY = {
  // Font Families
  FONT_SANS: "var(--font-sans)",
  FONT_SERIF: "var(--font-serif)",
  FONT_MONO: "var(--font-mono)",

  // Font Sizes
  FONT_SIZE_XS: "var(--font-size-xs)",
  FONT_SIZE_SM: "var(--font-size-sm)",
  FONT_SIZE_BASE: "var(--font-size-base)",
  FONT_SIZE_LG: "var(--font-size-lg)",
  FONT_SIZE_XL: "var(--font-size-xl)",
  FONT_SIZE_2XL: "var(--font-size-2xl)",
  FONT_SIZE_3XL: "var(--font-size-3xl)",
  FONT_SIZE_4XL: "var(--font-size-4xl)",
  FONT_SIZE_5XL: "var(--font-size-5xl)",
  FONT_SIZE_6XL: "var(--font-size-6xl)",

  // Font Weights
  FONT_WEIGHT_THIN: "var(--font-weight-thin)",
  FONT_WEIGHT_LIGHT: "var(--font-weight-light)",
  FONT_WEIGHT_NORMAL: "var(--font-weight-normal)",
  FONT_WEIGHT_MEDIUM: "var(--font-weight-medium)",
  FONT_WEIGHT_SEMIBOLD: "var(--font-weight-semibold)",
  FONT_WEIGHT_BOLD: "var(--font-weight-bold)",
  FONT_WEIGHT_EXTRABOLD: "var(--font-weight-extrabold)",
  FONT_WEIGHT_BLACK: "var(--font-weight-black)",

  // Line Heights
  LINE_HEIGHT_NONE: "var(--line-height-none)",
  LINE_HEIGHT_TIGHT: "var(--line-height-tight)",
  LINE_HEIGHT_SNUG: "var(--line-height-snug)",
  LINE_HEIGHT_NORMAL: "var(--line-height-normal)",
  LINE_HEIGHT_RELAXED: "var(--line-height-relaxed)",
  LINE_HEIGHT_LOOSE: "var(--line-height-loose)",

  // Letter Spacing
  TRACKING_TIGHTER: "var(--tracking-tighter)",
  TRACKING_TIGHT: "var(--tracking-tight)",
  TRACKING_NORMAL: "var(--tracking-normal)",
  TRACKING_WIDE: "var(--tracking-wide)",
  TRACKING_WIDER: "var(--tracking-wider)",
  TRACKING_WIDEST: "var(--tracking-widest)",
} as const;

// Shadows
export const SHADOWS = {
  "2XS": "var(--shadow-2xs)",
  XS: "var(--shadow-xs)",
  SM: "var(--shadow-sm)",
  MD: "var(--shadow-md)",
  LG: "var(--shadow-lg)",
  XL: "var(--shadow-xl)",
  "2XL": "var(--shadow-2xl)",
} as const;

// Transitions
export const TRANSITIONS = {
  FAST: "var(--transition-fast)",
  NORMAL: "var(--transition-normal)",
  SLOW: "var(--transition-slow)",
  BOUNCE: "var(--transition-bounce)",
  SPRING: "var(--transition-spring)",
} as const;

// Z-Index Scale
export const Z_INDEX = {
  DROPDOWN: "var(--z-dropdown)",
  STICKY: "var(--z-sticky)",
  FIXED: "var(--z-fixed)",
  MODAL_BACKDROP: "var(--z-modal-backdrop)",
  MODAL: "var(--z-modal)",
  POPOVER: "var(--z-popover)",
  TOOLTIP: "var(--z-tooltip)",
  TOAST: "var(--z-toast)",
} as const;

// Breakpoints
export const BREAKPOINTS = {
  SM: "var(--breakpoint-sm)",
  MD: "var(--breakpoint-md)",
  LG: "var(--breakpoint-lg)",
  XL: "var(--breakpoint-xl)",
  "2XL": "var(--breakpoint-2xl)",
} as const;

// Container Max Widths
export const CONTAINERS = {
  SM: "var(--container-sm)",
  MD: "var(--container-md)",
  LG: "var(--container-lg)",
  XL: "var(--container-xl)",
  "2XL": "var(--container-2xl)",
} as const;

// Utility Functions
export const designTokens = {
  // Get CSS variable value
  cssVar: (variable: keyof typeof CSS_VARS) => CSS_VARS[variable],

  // Get brand color
  brandColor: (color: keyof typeof BRAND_COLORS) => BRAND_COLORS[color],

  // Get Tailwind class
  tailwindClass: (className: keyof typeof TAILWIND_CLASSES) =>
    TAILWIND_CLASSES[className],

  // Get spacing value
  spacing: (size: keyof typeof SPACING) => SPACING[size],

  // Get border radius value
  borderRadius: (radius: keyof typeof BORDER_RADIUS) => BORDER_RADIUS[radius],

  // Get typography value
  typography: (property: keyof typeof TYPOGRAPHY) => TYPOGRAPHY[property],

  // Get transition value
  transition: (type: keyof typeof TRANSITIONS) => TRANSITIONS[type],

  // Get z-index value
  zIndex: (level: keyof typeof Z_INDEX) => Z_INDEX[level],

  // Get breakpoint value
  breakpoint: (size: keyof typeof BREAKPOINTS) => BREAKPOINTS[size],

  // Get container value
  container: (size: keyof typeof CONTAINERS) => CONTAINERS[size],
} as const;

// Type definitions for better TypeScript support
export type BrandColor = keyof typeof BRAND_COLORS;
export type CSSVariable = keyof typeof CSS_VARS;
export type TailwindClass = keyof typeof TAILWIND_CLASSES;
export type SpacingSize = keyof typeof SPACING;
export type BorderRadiusSize = keyof typeof BORDER_RADIUS;
export type TypographyProperty = keyof typeof TYPOGRAPHY;
export type TransitionType = keyof typeof TRANSITIONS;
export type ZIndexLevel = keyof typeof Z_INDEX;
export type BreakpointSize = keyof typeof BREAKPOINTS;
export type ContainerSize = keyof typeof CONTAINERS;

// Example usage:
// import { designTokens, CSS_VARS, TYPOGRAPHY, SPACING } from '@/lib/design-tokens';
//
// // In a component:
// <div className={designTokens.tailwindClass('PROGRESS_BAR')}>
//   <div className={designTokens.tailwindClass('PROGRESS_FILL')} />
// </div>
//
// // With CSS variables:
// <div style={{ backgroundColor: CSS_VARS.PRIMARY }}>
//   Content
// </div>
//
// // Typography:
// <h1 style={{
//   fontSize: CSS_VARS.FONT_SIZE_2XL,
//   fontWeight: CSS_VARS.FONT_WEIGHT_BOLD,
//   lineHeight: CSS_VARS.LINE_HEIGHT_TIGHT
// }}>
//   Heading
// </h1>
//
// // Spacing:
// <div style={{ padding: CSS_VARS.SPACING_4, margin: CSS_VARS.SPACING_6 }}>
//   Content with consistent spacing
// </div>
//
// // Transitions:
// <button style={{ transition: CSS_VARS.TRANSITION_NORMAL }}>
//   Hover me
// </button>
//
// // Z-Index:
// <modal style={{ zIndex: CSS_VARS.Z_MODAL }}>
//   Modal content
// </modal>
